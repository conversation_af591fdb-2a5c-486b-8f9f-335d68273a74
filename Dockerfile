# Multi-stage Dockerfile for Node.js SSE Application
FROM node:20-alpine AS base

# Enable pnpm using corepack
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

FROM base AS deps
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies with cache mount
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile

FROM base AS production
WORKDIR /app

# Set production environment
ENV NODE_ENV=production

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy application files
COPY package.json pnpm-lock.yaml ./
COPY tsconfig.json ./
COPY src ./src

# Copy environment file (will be overridden by Dokploy env vars)
COPY .env .env

# Expose the port
EXPOSE 3000

# Health check for zero-downtime deployments
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start the SSE server
CMD ["pnpm", "start"]
