{"name": "@nxxt/next-agent", "version": "1.0.5", "description": "Next Agent MCP servers for marketing research and repository analysis", "main": "index.js", "bin": "dist/stdio.js", "files": ["dist"], "scripts": {"dev": "<PERSON>ra dev", "build": "mastra build", "start": "npx tsx src/mastra/sse.ts", "build:mcp": "tsup src/mastra/stdio.ts --format esm --no-splitting --dts && chmod +x dist/stdio.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.23", "@mastra/core": "^0.11.1", "@mastra/libsql": "^0.11.2", "@mastra/loggers": "^0.10.4", "@mastra/mcp": "^0.10.7", "@mastra/memory": "^0.11.5", "@mendable/firecrawl-js": "^1.29.2", "@openrouter/ai-sdk-provider": "^0.7.3", "ai": "^4.3.19", "cheerio": "^1.1.2", "dotenv": "^17.2.1", "form-data": "^4.0.4", "node-fetch": "^3.3.2", "p-retry": "^6.2.1", "undici": "^7.12.0", "zod": "3"}, "devDependencies": {"@mastra/evals": "^0.10.7", "@types/node": "^24.1.0", "mastra": "^0.10.15", "prettier": "^3.6.2", "tsup": "^8.5.0", "typescript": "^5.8.3"}}