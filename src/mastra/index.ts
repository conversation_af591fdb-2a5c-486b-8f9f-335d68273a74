import { <PERSON><PERSON> } from "@mastra/core/mastra";
import { <PERSON><PERSON><PERSON>og<PERSON> } from "@mastra/loggers";
import { LibSQLStore } from "@mastra/libsql";
import { blackhatWorldAgent } from "./agents/blackhatworld-agent";
import { deepWikiAgent } from "./agents/deepwiki-agent";
import { githubAgent } from "./agents/github-agent";

export const mastra = new Mastra({
    agents: {
        blackhatWorldAgent,
        deepWikiAgent,
        githubAgent
    },
    storage: new LibSQLStore({
        // stores telemetry, evals, ... into memory storage, if it needs to persist, change to file:../mastra.db
        url: ":memory:"
    }),
    logger: new PinoLogger({
        name: "<PERSON><PERSON>",
        level: "info"
    })
});
