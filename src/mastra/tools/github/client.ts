/**
 * GitHub API Client
 * Handles repository search operations
 */

import { request } from "undici";

export interface GitHubSearchParams {
  query: string;
  searchType: "repositories";
  user?: string;
  org?: string;
  repo?: string;
  language?: string;
  stars?: string;
  forks?: string;
  size?: string;
  createdDaysAgo?: number;
  pushedDaysAgo?: number;
  topics?: string[];
  license?: string;
  is?: string[];
  archived?: boolean;
  sort?: string;
  order?: "asc" | "desc";
  per_page?: number;
  page?: number;
  // 新增高级搜索参数
  in?: string[]; // 搜索范围：description, readme, name, topics
  excludeTerms?: string[]; // 排除的关键词
}

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  clone_url: string;
  language: string | null;
  stargazers_count: number;
  forks_count: number;
  size: number;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  topics: string[];
  license: string | null;
  owner: {
    login: string;
    type: string;
    avatar_url: string;
  };
}

export interface GitHubSearchResponse {
  total_count: number;
  incomplete_results: boolean;
  items: GitHubRepository[];
}

export class GitHubClient {
  private baseUrl = "https://api.github.com";

  /**
   * Build search query string for GitHub API with advanced search syntax
   */
  private buildQuery(params: GitHubSearchParams): string {
    const queryParts: string[] = [];
    const qualifiers: string[] = [];

    // 处理基础搜索词 - 现在直接使用用户提供的查询，支持GitHub高级搜索语法
    if (params.query) {
      const searchTerms = this.processSearchTerms(params.query, params.in);
      if (searchTerms) {
        queryParts.push(searchTerms);
      }
    }

    // User/org/repo qualifiers
    if (params.user) qualifiers.push(`user:${params.user}`);
    if (params.org) qualifiers.push(`org:${params.org}`);
    if (params.repo) qualifiers.push(`repo:${params.repo}`);

    // Language
    if (params.language) qualifiers.push(`language:${params.language}`);

    // Numeric range searches
    if (params.stars) qualifiers.push(`stars:${params.stars}`);
    if (params.forks) qualifiers.push(`forks:${params.forks}`);
    if (params.size) qualifiers.push(`size:${params.size}`);

    // Date ranges - 转换 daysAgo 为具体日期
    if (params.createdDaysAgo !== undefined) {
      const createdDate = new Date();
      createdDate.setDate(createdDate.getDate() - params.createdDaysAgo);
      const createdDateStr = createdDate.toISOString().split("T")[0];
      qualifiers.push(`created:>${createdDateStr}`);
    }

    if (params.pushedDaysAgo !== undefined) {
      const pushedDate = new Date();
      pushedDate.setDate(pushedDate.getDate() - params.pushedDaysAgo);
      const pushedDateStr = pushedDate.toISOString().split("T")[0];
      qualifiers.push(`pushed:>${pushedDateStr}`);
    }

    // Topics (only for repository search)
    if (params.topics && params.searchType === "repositories") {
      params.topics.forEach(topic => qualifiers.push(`topic:${topic}`));
    }

    // License (only for repository search)
    if (params.license && params.searchType === "repositories") {
      qualifiers.push(`license:${params.license}`);
    }

    // Repository status (only for repository search)
    if (params.is && params.searchType === "repositories") {
      params.is.forEach(value => qualifiers.push(`is:${value}`));
    }

    // Archived status (only for repository search)
    if (params.archived !== undefined && params.searchType === "repositories") {
      qualifiers.push(`archived:${params.archived}`);
    }

    // 排除关键词
    if (params.excludeTerms) {
      params.excludeTerms.forEach(term => qualifiers.push(`-${term}`));
    }

    // 组合查询和限定符
    const finalQuery = [...queryParts, ...qualifiers].join(" ");

    console.log(`🔍 Built GitHub query: ${finalQuery}`);
    return finalQuery;
  }

  /**
   * 处理搜索词，支持GitHub高级搜索语法和in限定符
   */
  private processSearchTerms(query: string, inFields?: string[]): string {
    // 清理查询
    const cleanQuery = query.trim();
    if (!cleanQuery) return "";

    // 直接使用用户提供的查询，支持GitHub高级搜索语法
    // 用户可以直接在查询中使用 "exact phrases", (term1 OR term2), AND, NOT 等
    let searchTerms = cleanQuery;

    // 添加 in 限定符
    if (inFields && inFields.length > 0) {
      const inClause = `in:${inFields.join(",")}`;
      return `${searchTerms} ${inClause}`;
    }

    return searchTerms;
  }

  /**
   * Get GitHub token
   */
  private getToken(): string {
    return process.env.GITHUB_TOKEN || "";
  }

  /**
   * Execute search request
   */
  async search(params: GitHubSearchParams): Promise<GitHubSearchResponse> {
    const startTime = Date.now();

    try {
      console.log(`🔍 Searching GitHub repositories for: ${params.query.substring(0, 50)}...`);

      const token = this.getToken();
      const query = this.buildQuery(params);

      if (!query.trim()) {
        throw new Error("Search query cannot be empty");
      }

      // Build URL parameters
      const urlParams = new URLSearchParams({
        q: query,
        sort: params.sort || "best-match",
        order: params.order || "desc",
        per_page: String(params.per_page || 30),
        page: String(params.page || 1)
      });

      const url = `${this.baseUrl}/search/repositories?${urlParams}`;

      // Set headers
      const headers: Record<string, string> = {
        Accept: "application/vnd.github.v3+json",
        "User-Agent": "Mastra-GitHub-Search"
      };

      if (token) {
        headers["Authorization"] = `token ${token}`;
      }

      // Send request
      const { statusCode, body } = await request(url, {
        method: "GET",
        headers,
        headersTimeout: 0,
        bodyTimeout: 0
      });

      if (statusCode !== 200) {
        throw new Error(`GitHub API request failed: ${statusCode}`);
      }

      const data = (await body.json()) as GitHubSearchResponse;
      const executionTime = Date.now() - startTime;

      console.log(`✅ GitHub search completed in ${executionTime}ms: ${data.total_count} results`);

      return data;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ GitHub search failed after ${executionTime}ms:`, error);
      throw new Error(`GitHub search failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
