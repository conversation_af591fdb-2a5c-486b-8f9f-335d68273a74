import { ProxyAgent, setGlobalDispatcher } from "undici";
const proxy = new ProxyAgent("**********************************************/");

setGlobalDispatcher(proxy);
fetch("https://www.blackhatworld.com/search/search", {
    headers: {
        "accept": "application/json",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "multipart/form-data; boundary=----WebKitFormBoundaryOtctrKaH4yePSimO",
        "priority": "u=1, i",
        "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
        "sec-ch-ua-arch": "\"arm\"",
        "sec-ch-ua-bitness": "\"64\"",
        "sec-ch-ua-full-version": "\"137.0.7151.104\"",
        "sec-ch-ua-full-version-list": "\"Google Chrome\";v=\"137.0.7151.104\", \"Chromium\";v=\"137.0.7151.104\", \"Not/A)Brand\";v=\"********\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-model": "\"\"",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-ch-ua-platform-version": "\"15.3.0\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        "cookie": "cf_clearance=ceSwRK7GsK5D_lJk5TAmyzf.WsjOLCMMboIUCF3UAYI-1729342286-*******-vRQWX37Ntl76SgMFIPPcSoDeX0.BZLRfg_dSNcQ.nmNeTgzb1GSHO1lL4wpixr1SlloKd258M89qmaKsekU9QncCVLYzdbSt4fDrd3MzF91T_1OJuczfpaeCpg0Di_t._aZkmzGMo6iqzZvCAJG469o9lRJ3fgmev38mvdX.wAd4gR7pPVJA8jxssG52AoddU4PJLVx1dpb0TjzBwvYkKvCMx.NRsMqpCGRyDLZBadD61FeQ_uiLTRwJ2yUP3L8vIaNcaCCBmrmNi0kInorJnhcPK8aOJh9GiJ86QHzOAyAsCZx3nfzypIvfPdQAT41htshCXOvcIUeYabSvLz46Q3R8IJF7.KhD3vMLgRajhIejV8qyIKXPszNZglkdDspFhmAv59HkpY2zGHSdyBjWsl3JXuLk2k4hEFar1TDrNUpz8t2pHJ09sbc39wfjCl3c; _ga=GA1.1.1501681644.1731226894; xf_csrf=SPyReMlbN5AU3R5Z; xf_session=cEFxL9jeiWax0LOXEDvprMpgHXR4DZpi; _ga_VH2PZEKYEE=GS2.1.s1754311146$o21$g1$t1754315109$j57$l0$h0",
        "Referer": "https://www.blackhatworld.com/search/?type=post",
        "Referrer-Policy": "same-origin"
    },
    "body": "------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"_xfToken\"\r\n\r\n1754311172,88af7a6a0e4dd34384c80477469ab060\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"keywords\"\r\n\r\n?\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"c[users]\"\r\n\r\n\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"c[newer_than]\"\r\n\r\n2025-07-28\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"c[older_than]\"\r\n\r\n2025-08-04\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"c[min_reply_count]\"\r\n\r\n10\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"c[child_nodes]\"\r\n\r\n1\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"order\"\r\n\r\ndate\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"grouped\"\r\n\r\n1\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"search_type\"\r\n\r\npost\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"_xfResponseType\"\r\n\r\njson\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"_xfWithData\"\r\n\r\n1\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"_xfRequestUri\"\r\n\r\n/search/?type=post\r\n------WebKitFormBoundaryOtctrKaH4yePSimO\r\nContent-Disposition: form-data; name=\"_xfToken\"\r\n\r\n1754311172,88af7a6a0e4dd34384c80477469ab060\r\n------WebKitFormBoundaryOtctrKaH4yePSimO--\r\n",
    "method": "POST"
}).then(res => res.json()).then(data => {
    console.log(data);
}).catch(err => {
    console.log(err);
});