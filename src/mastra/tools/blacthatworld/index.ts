import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { search, getNodesDescription } from "./search";
import { scrapeArticle } from "./scraper";
import FireCrawlApp from "@mendable/firecrawl-js";
import * as cheerio from "cheerio";

const app = new FireCrawlApp({ apiKey: process.env.FIRECRAWL_API_KEY || "fc-dd20fa1c0ec4435a871e535d538b4b2f" });

// 搜索结果项接口
interface SearchResultItem {
  title: string;
  url: string;
  author: string;
  replies: number;
  views: number;
  createdAt: string; // 帖子发布时间 (ISO 8601 格式，如: "2025-08-01T19:51:11+0100")
  forum: string;
  snippet?: string;
}

// 搜索结果接口
interface SearchResults {
  searchUrl: string;
  results: SearchResultItem[];
  currentPage: number;
  hasNextPage: boolean;
  maxPage: number;
}

/**
 * 解析搜索结果页面
 */
const parseSearchResults = async (searchUrl: string, newerThan?: string, olderThan?: string): Promise<SearchResults> => {
  try {
    const scrapeResult = await app.scrapeUrl(searchUrl, {
      formats: ["rawHtml"],
      onlyMainContent: false,
      parsePDF: false,
      maxAge: 14400000
    });

    if (!scrapeResult.success || !scrapeResult.rawHtml) {
      throw new Error("Failed to scrape search results");
    }

    const $ = cheerio.load(scrapeResult.rawHtml);
    const results: SearchResultItem[] = [];

    // 解析搜索结果项 - 基于新的HTML结构
    const searchItems = $("li.block-row");

    // 获取最大页数
    const maxPage = $(".pageNav-main li:last-child a").text();

    searchItems.each((index, element) => {
      const $item = $(element);

      // 获取标题和链接
      const titleElement = $item.find(".contentRow-title a").first();
      const title = titleElement.text().trim();
      const url = titleElement.attr("href");

      if (!title || !url) {
        return;
      }

      // 清理标题中的高亮标签 - 使用jQuery的text()方法自动清理HTML
      const cleanTitle = titleElement.clone().find("em").remove().end().text().trim();

      // 获取作者
      const author = $item.find(".contentRow-minor .username").first().text().trim();

      // 获取摘要 - 使用jQuery的text()方法自动清理HTML标签
      const snippetElement = $item.find(".contentRow-snippet");
      const snippet = snippetElement.clone().find("em").remove().end().text().trim();

      // 解析元数据列表
      const metaItems = $item.find(".contentRow-minor .listInline li");
      let replies = 0;
      let views = 0;
      let createdAt = "";
      let forum = "";
      let tags: string[] = [];

      metaItems.each((metaIndex, metaElement) => {
        const $meta = $(metaElement);
        const text = $meta.text().trim();

        if (metaIndex + 1 === 4) {
          tags = text?.replace(/\t/g, "").split("\n").filter(Boolean) || [];
        }

        // 解析回复数
        if (text.startsWith("Replies:")) {
          const repliesMatch = text.match(/Replies:\s*(\d+)/);
          if (repliesMatch) {
            replies = parseInt(repliesMatch[1]) || 0;
          }
        }

        // 解析浏览数 (如果存在)
        if (text.startsWith("Views:")) {
          const viewsMatch = text.match(/Views:\s*(\d+)/);
          if (viewsMatch) {
            views = parseInt(viewsMatch[1]) || 0;
          }
        }

        // 解析论坛
        if (text.startsWith("Forum:")) {
          const forumLink = $meta.find("a");
          forum = forumLink.length > 0 ? forumLink.text().trim() : text.replace("Forum:", "").trim();
        }

        // 解析时间 - 注意：这里获取的是帖子的发布时间，不是最后回复时间
        // datetime 格式示例: "2025-08-01T19:51:11+0100"
        const timeElement = $meta.find("time");
        if (timeElement.length > 0) {
          createdAt = timeElement.attr("datetime") || "";
        }
      });

      // 如果没有找到views，尝试其他方式获取或设置默认值
      if (views === 0) {
        // 可以根据回复数估算浏览数，或者设置默认值
        views = replies * 10; // 简单估算
      }

      // 日期过滤逻辑
      let shouldInclude = true;
      if (createdAt && (newerThan || olderThan)) {
        try {
          const postDate = new Date(createdAt);
          const postDateStr = postDate.toISOString().split("T")[0]; // 转换为 YYYY-MM-DD 格式

          if (newerThan && postDateStr < newerThan) {
            shouldInclude = false;
          }
          if (olderThan && postDateStr > olderThan) {
            shouldInclude = false;
          }
        } catch (error) {
          console.warn("Failed to parse date:", createdAt, error);
          // 如果日期解析失败，保持包含该项目
        }
      }

      if (shouldInclude) {
        const resultItem = {
          title: cleanTitle,
          url: url.startsWith("http") ? url : `https://www.blackhatworld.com${url}`,
          author,
          replies,
          views,
          createdAt,
          forum,
          snippet,
          tags
        };

        results.push(resultItem);
      }
    });

    // 解析分页信息
    const currentPage = parseInt($(".pageNav-page--current").text()) || 1;
    const hasNextPage = $(".pageNav-jump--next").length > 0;

    return {
      searchUrl,
      results,
      currentPage,
      hasNextPage,
      maxPage: parseInt(maxPage || "1")
    };
  } catch (error) {
    console.error("Error parsing search results:", error);
    // 出错时返回空结果
    return {
      searchUrl,
      results: [],
      currentPage: 1,
      hasNextPage: false,
      maxPage: 1
    };
  }
};

const threads = getNodesDescription();

/**
 * 单个文章抓取的核心逻辑 - 遵循DRY原则
 */
async function executeSingleScrape(url: string) {
  try {
    const article = await scrapeArticle(url);

    if (!article) {
      return {
        url,
        title: "",
        content: "",
        author: "",
        postDate: "",
        replies: 0,
        views: 0,
        success: false
      };
    }

    return {
      ...article,
      success: true
    };
  } catch (error) {
    console.error("Article scraping error:", error);
    return {
      url,
      title: "",
      content: "",
      author: "",
      postDate: "",
      replies: 0,
      views: 0,
      success: false
    };
  }
}

/**
 * 批量抓取文章的核心逻辑 - 支持并发控制
 */
async function executeBatchScrape(urls: string[], concurrent: boolean = true, maxConcurrency: number = 3) {
  if (!concurrent || urls.length <= 1) {
    // 串行执行
    const results = [];
    for (const url of urls) {
      const result = await executeSingleScrape(url);
      results.push(result);
    }
    return results;
  }

  // 并发执行，控制并发数量 - 分批处理
  const results = [];

  for (let i = 0; i < urls.length; i += maxConcurrency) {
    const batch = urls.slice(i, i + maxConcurrency);
    const batchPromises = batch.map(url => executeSingleScrape(url));
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
  }

  return results;
}

/**
 * 通用的 BlackHatWorld 搜索执行函数
 */
async function executeBlackhatWorldSearch(params: {
  keywords: string;
  nodeNames?: string[];
  daysAgo?: number;
  order?: "relevance" | "date" | "replies";
  pages?: number;
  minReplies?: number;
}) {
  const { keywords, pages = 1, nodeNames, daysAgo, order = "relevance", minReplies = 5 } = params;

  try {
    // 根据 daysAgo 计算日期范围
    let newerThan = "";
    let olderThan = "";

    if (daysAgo && daysAgo > 0) {
      const now = new Date();
      const startDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);

      // 格式化为 YYYY-MM-DD
      newerThan = startDate.toISOString().split("T")[0];
      olderThan = now.toISOString().split("T")[0];
    }

    // 执行搜索获取重定向URL
    const searchUrl = await search(keywords, nodeNames, newerThan, olderThan, minReplies, order);

    if (!searchUrl) {
      return {
        searchUrl: "",
        results: ["No results found"],
        maxPage: 1
      };
    }
    // 解析搜索结果
    const searchResults = await parseSearchResults(searchUrl, newerThan, olderThan);

    // 获取额外页面的结果
    const moreSearchResults = await Promise.all(
      Array.from({ length: pages - 1 }, async (_, index) => {
        const page = index + 2;
        const pageUrl = `${searchUrl}&page=${page}`;
        const pageResults = await parseSearchResults(pageUrl, newerThan, olderThan);
        return pageResults.results;
      })
    );

    return {
      searchUrl: searchResults.searchUrl,
      results: [...searchResults.results, ...moreSearchResults.flat()],
      maxPage: searchResults.maxPage
    };
  } catch (error) {
    console.error("BlackHatWorld search failed:", error);
    return {
      searchUrl: "",
      results: [],
      maxPage: 1
    };
  }
}

/**
 * BlackHatWorld 内容搜索工具 - 用于搜索特定主题和关键词
 */
export const blackhatWorldSearchTool = createTool({
  id: "blackhatworld-search",
  description:
    "Search BlackHatWorld forum for specific topics and discussions using keywords. Use this when you have specific search terms or topics to find relevant posts and discussions.",
  inputSchema: z.object({
    keywords: z
      .string()
      .describe("Search keywords for specific topics (required for targeted search), do not contain any date or time"),
    nodeNames: z
      .array(z.string())
      .optional()
      .describe(`Forum node names to search in (e.g., ["Making Money", "Black Hat SEO"])\n\n${threads}`),
    daysAgo: z.number().optional().describe("Number of days ago to search from (e.g., 7 for last 7 days, 30 for last 30 days)"),
    order: z
      .enum(["relevance", "date", "replies"])
      .optional()
      .default("relevance")
      .describe("Sort order - relevance is best for keyword searches"),
    pages: z.number().optional().default(1).describe("Number of pages to scrape, 1 page = 20 results"),
    minReplies: z.number().optional().default(5).describe("Minimum number of replies to include in the results")
  }),
  outputSchema: z.object({
    searchUrl: z.string(),
    results: z
      .array(
        z.object({
          title: z.string(),
          url: z.string(),
          author: z.string(),
          replies: z.number(),
          views: z.number(),
          createdAt: z.string(),
          forum: z.string(),
          snippet: z.string().optional(),
          tags: z.array(z.string()).optional()
        })
      )
      .optional(),
    maxPage: z.number().optional()
  }),
  execute: async ({ context }) => {
    const { keywords, pages, nodeNames, daysAgo, order, minReplies } = context;

    return await executeBlackhatWorldSearch({
      keywords,
      nodeNames,
      daysAgo,
      order,
      pages,
      minReplies
    });
  }
});

/**
 * BlackHatWorld 文章抓取工具
 */
export const blackhatWorldScrapeTool = createTool({
  id: "blackhatworld-scrape",
  description: "Scrape article content from BlackHatWorld forum posts",
  inputSchema: z.object({
    url: z.string().describe("URL of the forum post to scrape")
    // allReplies: z.boolean().optional().default(false).describe("Whether to scrape all replies")
  }),
  outputSchema: z.object({
    url: z.string(),
    title: z.string(),
    content: z.string(),
    author: z.string(),
    postDate: z.string(),
    replies: z.number(),
    views: z.number(),
    posts: z
      .array(
        z.object({
          postNumber: z.number(),
          author: z.string(),
          userTitle: z.string(),
          content: z.string(),
          date: z.string().nullable(),
          likes: z.number(),
          isMainPost: z.boolean()
        })
      )
      .optional(),
    totalPosts: z.number().optional(),
    mainPost: z
      .object({
        postNumber: z.number(),
        author: z.string(),
        userTitle: z.string(),
        content: z.string(),
        date: z.string().nullable(),
        likes: z.number(),
        isMainPost: z.boolean().describe("Whether the post is the main post")
      })
      .nullable()
      .optional(),
    threadId: z.string().nullable().optional(),
    forum: z.string().optional(),
    tags: z.array(z.string()).optional(),
    extractedAt: z.string().optional(),
    success: z.boolean()
  }),
  execute: async ({ context }) => {
    const { url } = context;
    return await executeSingleScrape(url);
  }
});

/**
 * BlackHatWorld 批量文章抓取工具
 */
export const blackhatWorldBatchScrapeTool = createTool({
  id: "blackhatworld-batch-scrape",
  description: "Batch scrape multiple article contents from BlackHatWorld forum posts with concurrency control",
  inputSchema: z.object({
    urls: z.array(z.string()).min(1).max(10).describe("Array of URLs of the forum posts to scrape (max 10 URLs)"),
    concurrent: z.boolean().optional().default(true).describe("Whether to run scraping concurrently for faster results"),
    maxConcurrency: z.number().min(1).max(3).optional().default(3).describe("Maximum number of concurrent scraping operations (max 3)")
  }),
  outputSchema: z.object({
    results: z.array(z.object({
      url: z.string(),
      title: z.string(),
      content: z.string(),
      author: z.string(),
      postDate: z.string(),
      replies: z.number(),
      views: z.number(),
      posts: z
        .array(
          z.object({
            postNumber: z.number(),
            author: z.string(),
            userTitle: z.string(),
            content: z.string(),
            date: z.string().nullable(),
            likes: z.number(),
            isMainPost: z.boolean()
          })
        )
        .optional(),
      totalPosts: z.number().optional(),
      mainPost: z
        .object({
          postNumber: z.number(),
          author: z.string(),
          userTitle: z.string(),
          content: z.string(),
          date: z.string().nullable(),
          likes: z.number(),
          isMainPost: z.boolean().describe("Whether the post is the main post")
        })
        .nullable()
        .optional(),
      threadId: z.string().nullable().optional(),
      forum: z.string().optional(),
      tags: z.array(z.string()).optional(),
      extractedAt: z.string().optional(),
      success: z.boolean()
    })),
    totalUrls: z.number(),
    successCount: z.number(),
    failureCount: z.number(),
    executionTime: z.number().describe("Execution time in milliseconds")
  }),
  execute: async ({ context }) => {
    const { urls, concurrent = true, maxConcurrency = 3 } = context;

    const startTime = Date.now();
    const results = await executeBatchScrape(urls, concurrent, maxConcurrency);
    const executionTime = Date.now() - startTime;

    const successCount = results.filter(result => result.success).length;
    const failureCount = results.length - successCount;

    return {
      results,
      totalUrls: urls.length,
      successCount,
      failureCount,
      executionTime
    };
  }
});

/**
 * BlackHatWorld 热度浏览工具 - 用于浏览论坛最新热门讨论
 */
export const blackhatWorldTrendingTool = createTool({
  id: "blackhatworld-trending",
  description:
    "Browse trending and recent discussions in BlackHatWorld forum by category. Use this when you want to see what's hot and popular in specific forum sections without searching for specific keywords, do not contain any date or time",
  inputSchema: z.object({
    nodeNames: z
      .array(z.string())
      .describe(`Forum node names to browse for trending content (e.g., ["Making Money", "Black Hat SEO"])\n\n${threads}`),
    daysAgo: z.number().optional().default(7).describe("Number of days ago to check for trending content (default: 7 days)"),
    order: z
      .enum(["date", "replies"])
      .optional()
      .default("date")
      .describe("Sort order - date for latest posts, replies for most active discussions"),
    pages: z.number().min(3).optional().default(3).describe("Number of pages to scrape, 1 page = 20 results"),
    minReplies: z
      .number()
      .optional()
      .default(10)
      .describe("Minimum number of replies to include (higher threshold for trending content)")
  }),
  outputSchema: z.object({
    searchUrl: z.string(),
    results: z
      .array(
        z.object({
          title: z.string(),
          url: z.string(),
          author: z.string(),
          replies: z.number(),
          views: z.number(),
          createdAt: z.string(),
          forum: z.string(),
          snippet: z.string().optional(),
          tags: z.array(z.string()).optional()
        })
      )
      .optional(),
    maxPage: z.number().optional()
  }),
  execute: async ({ context }) => {
    const { pages, nodeNames, daysAgo, order, minReplies } = context;

    // 对于热度浏览，使用通配符搜索
    const keywords = "?";

    return await executeBlackhatWorldSearch({
      keywords,
      nodeNames,
      daysAgo,
      order,
      pages,
      minReplies
    });
  }
});

// 测试代码 - 可以在开发时使用
// executeBlackhatWorldSearch({
//   keywords: "OnlyFans $15000 monthly",
//   nodeNames: ["Affiliate Programs", "CPA", "Making Money"],
//   daysAgo: 60,
//   order: "replies",
//   pages: 1,
//   minReplies: 10
// }).then(console.log);



// 批量抓取测试示例
// executeBatchScrape([
//   "https://www.blackhatworld.com/seo/example-post-1.html",
//   "https://www.blackhatworld.com/seo/example-post-2.html"
// ], true, 2).then(results => {
//   console.log("Batch scrape results:", results);
// });
