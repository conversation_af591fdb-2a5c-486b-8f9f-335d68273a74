import FireCrawlApp from "@mendable/firecrawl-js";
import * as cheerio from "cheerio";

const app = new FireCrawlApp({ apiKey: process.env.FIRECRAWL_API_KEY || "fc-dd20fa1c0ec4435a871e535d538b4b2f" });

// 线程信息接口
interface ThreadInfo {
  url: string;
  title: string;
  replies: number;
  views: number;
  author: string;
  lastPost: string;
}

// 帖子内容接口
interface PostContent {
  postNumber: number;
  author: string;
  userTitle: string;
  content: string;
  date: string | null;
  likes: number;
  isMainPost: boolean;
}

// 文章内容接口
interface ArticleContent {
  url: string;
  title: string;
  content: string;
  author: string;
  postDate: string;
  replies: number;
  views: number;
  posts: PostContent[];
  totalPosts: number;
  mainPost: PostContent | null;
  threadId: string | null;
  forum: string;
  tags: string[];
  extractedAt: string;
}

/**
 * 从论坛页面解析有回复的线程链接
 */
export const resolveThreads = async (forumUrl: string, minReplies: number = 5): Promise<ThreadInfo[]> => {
  try {
    const scrapeResult = await app.scrapeUrl(forumUrl, {
      formats: ["rawHtml"],
      onlyMainContent: false,
      parsePDF: false,
      maxAge: 14400000
    });

    if (!scrapeResult.success || !scrapeResult.rawHtml) {
      throw new Error("Failed to scrape forum page");
    }

    const $ = cheerio.load(scrapeResult.rawHtml);
    const threads: ThreadInfo[] = [];

    $(".structItemContainer-group .structItem:not(:has(.sticky-thread--hightlighted))").each((_, element) => {
      const $item = $(element);

      const titleElement = $item.find("a[data-preview-url]").first();
      const title = titleElement.text().trim();
      const url = titleElement.attr("href");

      if (!title || !url) return;

      const repliesText = $item.find(".structItem-cell--meta dl:first-child dd").text().trim();
      const viewsText = $item.find(".structItem-cell--meta dl:nth-child(2) dd").text().trim();
      const replies = parseInt(repliesText.replace(/,/g, "")) || 0;
      const views = parseInt(viewsText.replace(/,/g, "")) || 0;

      // 只包含回复数大于指定值的线程
      if (replies >= minReplies) {
        const author = $item.find(".username").first().text().trim();
        const lastPost =
          $item.find(".structItem-cell--latest time").attr("title") || $item.find(".structItem-cell--latest time").text().trim();

        threads.push({
          url: url.startsWith("http") ? url : `https://www.blackhatworld.com${url}`,
          title,
          replies,
          views,
          author,
          lastPost
        });
      }
    });

    console.log(`Found ${threads.length} threads with ${minReplies}+ replies`);
    return threads;
  } catch (error) {
    console.error("Error resolving threads:", error);
    return [];
  }
};

/**
 * 解析数字（处理逗号、K、M后缀）
 */
const parseNumber = (text: string): number => {
  if (!text) return 0;

  const cleaned = text.replace(/,/g, "").toLowerCase();
  const match = cleaned.match(/(\d+(?:\.\d+)?)\s*([km]?)/);

  if (!match) return 0;

  let num = parseFloat(match[1]);
  const suffix = match[2];

  if (suffix === "k") num *= 1000;
  if (suffix === "m") num *= 1000000;

  return Math.floor(num);
};

/**
 * 解析日期
 */
const parseDate = (element: any): string | null => {
  if (!element) return null;

  // 尝试获取datetime属性
  const datetime = element.attr ? element.attr("datetime") : null;
  if (datetime) {
    return new Date(datetime).toISOString();
  }

  // 尝试解析文本内容
  const text = element.text ? element.text().trim() : element.toString();
  if (text) {
    const date = new Date(text);
    if (!isNaN(date.getTime())) {
      return date.toISOString();
    }
  }

  return null;
};

/**
 * 清理HTML内容，转换为纯文本
 */
const cleanContent = (html: string): string => {
  if (!html) return "";

  // 使用cheerio解析HTML并提取纯文本
  const $ = cheerio.load(html);

  // 移除不需要的元素
  $("script, style, .bbCodeBlock, .message-signature, .quote, .spoiler-header").remove();

  // 处理特殊元素
  $("br").replaceWith("\n");
  $("p").each((_, el) => {
    $(el).after("\n\n");
  });
  $("div").each((_, el) => {
    $(el).after("\n");
  });
  $("li").each((_, el) => {
    $(el).prepend("• ").after("\n");
  });
  $("h1, h2, h3, h4, h5, h6").each((_, el) => {
    $(el).after("\n\n");
  });

  // 提取纯文本
  let text = $.text();

  // 清理和格式化文本
  text = text
    .replace(/\n\s*\n\s*\n/g, "\n\n") // 移除多余的空行
    .replace(/[ \t]+/g, " ") // 标准化空格
    .replace(/^\s+|\s+$/gm, "") // 移除行首行尾空白
    .trim();

  return text;
};

/**
 * 从URL提取线程ID
 */
const extractThreadIdFromUrl = (url: string): string | null => {
  const match = url.match(/\.(\d+)\//);
  return match ? match[1] : null;
};

/**
 * 从URL提取标题
 */
const extractTitleFromUrl = (url: string): string => {
  const urlParts = url.split("/");
  const titlePart = urlParts[urlParts.length - 2];
  if (titlePart) {
    return titlePart.replace(/[-_]/g, " ").replace(/\.\d+$/, "");
  }
  return "Unknown Title";
};

/**
 * 抓取单个文章内容 - 优化版本
 */
export const scrapeArticle = async (url: string): Promise<ArticleContent | null> => {
  try {
    console.log(`🔍 Scraping article: ${url}`);

    // 同时获取HTML和Markdown格式
    const scrapeResult = await app.scrapeUrl(url, {
      formats: ["rawHtml", "markdown"],
      onlyMainContent: false,
      parsePDF: false,
      maxAge: 14400000
    });

    if (!scrapeResult.success) {
      console.error("Failed to scrape article:", url);
      return null;
    }

    const $ = cheerio.load(scrapeResult.rawHtml || "");
    const posts: PostContent[] = [];

    console.log(`📄 Parsing thread page structure...`);

    // 解析所有帖子（主帖 + 回复）
    const postElements = $('.message, .js-post, [data-content="message"]');
    console.log(`Found ${postElements.length} post elements`);

    postElements.each((index, element) => {
      const $post = $(element);

      // 提取作者信息
      const author =
        $post.find(".username, .message-name, .p-title-value").first().text().trim() ||
        $post.find("[data-user-id]").first().text().trim() ||
        "Unknown";

      // 提取用户头衔
      const userTitle = $post.find(".userTitle, .message-userTitle, .userBanner").first().text().trim();

      // 提取帖子内容
      const contentElement = $post.find(".message-content, .bbWrapper, .messageText");
      const content = contentElement.length > 0 ? cleanContent(contentElement.html() || "") : "";

      // 提取日期
      const dateElement = $post.find("time, .message-date, .DateTime");
      const date = parseDate(dateElement.first());

      // 提取点赞数
      const likeElement = $post.find(".message-likes, .likes, .reaction-count");
      const likes = parseNumber(likeElement.text());

      // 提取帖子编号
      const postNumberElement = $post.find(".message-number, .post-number");
      const postNumber = parseNumber(postNumberElement.text()) || index + 1;

      if (content.length > 10) {
        // 只包含有实际内容的帖子
        const post: PostContent = {
          postNumber,
          author,
          userTitle,
          content,
          date,
          likes,
          isMainPost: index === 0
        };

        posts.push(post);
      }
    });

    // 提取线程元数据
    const title = $("h1.p-title-value, .titleBar h1, .thread-title").first().text().trim() || extractTitleFromUrl(url);

    // 提取论坛信息
    const forumElement = $(".p-breadcrumbs a, .breadcrumb a").last();
    const forum = forumElement.text().trim() || "Unknown Forum";

    // 提取标签
    const tags: string[] = [];
    $(".tagItem, .tag, .thread-tag").each((_, tagElement) => {
      const tag = $(tagElement).text().trim();
      if (tag) tags.push(tag);
    });

    // 提取回复数和浏览数
    const statsElement = $(".pairs, .thread-stats, .message-stats");
    let replies = 0;
    let views = 0;

    statsElement.find("dt, dd, .pair-title, .pair-value").each((_, statElement) => {
      const text = $(statElement).text().trim().toLowerCase();
      if (text.includes("replies") || text.includes("回复")) {
        const nextElement = $(statElement).next();
        replies = parseNumber(nextElement.text());
      }
      if (text.includes("views") || text.includes("浏览")) {
        const nextElement = $(statElement).next();
        views = parseNumber(nextElement.text());
      }
    });

    // 如果没有找到统计数据，使用帖子数量估算
    if (replies === 0) replies = Math.max(0, posts.length - 1);
    if (views === 0) views = replies * 15; // 估算浏览数

    const result: ArticleContent = {
      url,
      title,
      content: posts.map(p => p.content).join("\n\n"), // 使用解析的纯文本内容
      author: posts[0]?.author || "Unknown",
      postDate: posts[0]?.date || new Date().toISOString(),
      replies,
      views,
      posts,
      totalPosts: posts.length,
      mainPost: posts[0] || null,
      threadId: extractThreadIdFromUrl(url),
      forum,
      tags,
      extractedAt: new Date().toISOString()
    };

    console.log(`✅ Successfully parsed article:`);
    console.log(`   - Title: ${result.title}`);
    console.log(`   - Author: ${result.author}`);
    console.log(`   - Posts: ${result.totalPosts}`);
    console.log(`   - Replies: ${result.replies}`);
    console.log(`   - Views: ${result.views}`);
    console.log(`   - Forum: ${result.forum}`);
    console.log(`   - Tags: ${result.tags.join(", ")}`);

    return result;
  } catch (error) {
    console.error("Error scraping article:", error);
    return null;
  }
};

/**
 * 批量抓取多个论坛页面的线程
 */
export const scrapeMultipleForums = async (forumUrls: string[], minReplies: number = 5): Promise<ThreadInfo[]> => {
  const allThreads: ThreadInfo[] = [];

  for (const forumUrl of forumUrls) {
    console.log(`Scraping forum: ${forumUrl}`);
    const threads = await resolveThreads(forumUrl, minReplies);
    allThreads.push(...threads);
    console.log(`Found ${threads.length} valid threads from this forum`);
  }

  console.log(`Total threads found: ${allThreads.length}`);
  return allThreads;
};

/**
 * 预定义的热门论坛URL
 */
export const POPULAR_FORUMS = [
  "https://www.blackhatworld.com/forums/making-money.12/?last_days=7&order=post_date&direction=desc",
  "https://www.blackhatworld.com/forums/black-hat-seo.1/?last_days=7&order=post_date&direction=desc",
  "https://www.blackhatworld.com/forums/white-hat-seo.95/?last_days=7&order=post_date&direction=desc",
  "https://www.blackhatworld.com/forums/social-media.270/?last_days=7&order=post_date&direction=desc"
];

/**
 * 快速获取热门线程
 */
export const getPopularThreads = async (minReplies: number = 10): Promise<ThreadInfo[]> => {
  return await scrapeMultipleForums(POPULAR_FORUMS, minReplies);
};

/**
 * 测试内容清理功能
 */
export const testContentCleaning = () => {
  console.log("🧪 Testing content cleaning functionality...\n");

  const testHtml = `
    <div class="message-content">
      <p>This is a <strong>test</strong> paragraph with <em>formatting</em>.</p>
      <div class="bbCodeBlock">
        <div class="quote">This is a quote that should be removed</div>
      </div>
      <ul>
        <li>First item</li>
        <li>Second item</li>
      </ul>
      <h3>Heading</h3>
      <p>Another paragraph with <a href="#">a link</a>.</p>
      <script>alert('malicious script');</script>
      <div class="message-signature">User signature</div>
    </div>
  `;

  const cleaned = cleanContent(testHtml);

  console.log("Original HTML:");
  console.log(testHtml);
  console.log("\nCleaned content:");
  console.log(cleaned);
  console.log("\nContent length:", cleaned.length);

  // 验证清理结果
  const shouldNotContain = ["<", ">", "script", "signature", "quote"];
  const shouldContain = ["test paragraph", "First item", "Second item", "Heading", "Another paragraph"];

  console.log("\n✅ Validation:");
  shouldNotContain.forEach(item => {
    const contains = cleaned.includes(item);
    console.log(`  ${contains ? "❌" : "✅"} Should not contain "${item}": ${!contains}`);
  });

  shouldContain.forEach(item => {
    const contains = cleaned.includes(item);
    console.log(`  ${contains ? "✅" : "❌"} Should contain "${item}": ${contains}`);
  });

  return cleaned;
};
