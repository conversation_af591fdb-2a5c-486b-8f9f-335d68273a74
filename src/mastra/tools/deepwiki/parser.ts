/**
 * DeepWiki 响应解析器
 * 解析和处理 DeepWiki API 响应数据
 */

import type { DeepWikiResponse, ResponseItem, FileReference, FileContent } from "./client.js";

// ============================================================================
// 解析结果类型
// ============================================================================

export interface ParsedResult {
  chunks: string[];
  references: FileReference[];
  fileContents: FileContent[];
  summary: string;
  combinedContent: string; // 新增：拼接的总结内容
  codeSnippets: CodeSnippet[]; // 新增：解析后的代码片段
}

export interface CodeSnippet {
  repo: string;
  filePath: string;
  content: string;
  startLine: number;
  endLine: number;
  context: string;
}

// ============================================================================
// DeepWiki 响应解析器
// ============================================================================

export class DeepWikiParser {
  /**
   * 解析 DeepWiki 响应
   */
  parseResponse(response: DeepWikiResponse): ParsedResult {
    console.log("Parsing DeepWiki response", {
      queriesCount: response.queries?.length || 0
    });

    const result: ParsedResult = {
      chunks: [],
      references: [],
      fileContents: [],
      summary: "",
      combinedContent: "",
      codeSnippets: []
    };

    if (!response.queries || response.queries.length === 0) {
      console.warn("No queries data to parse");
      return result;
    }

    // 解析所有查询的响应项
    for (const query of response.queries) {
      if (query.response && query.response.length > 0) {
        for (const item of query.response) {
          this.parseResponseItem(item, result);
        }
      }
    }

    // 处理解析后的数据
    result.codeSnippets = this.extractCodeSnippets(result);
    result.combinedContent = this.generateCombinedContent(result);

    console.log("Parsed response", {
      chunks: result.chunks.length,
      references: result.references.length,
      fileContents: result.fileContents.length,
      codeSnippets: result.codeSnippets.length
    });

    return result;
  }

  /**
   * 解析单个响应项
   */
  private parseResponseItem(item: ResponseItem, result: ParsedResult): void {
    switch (item.type) {
      case "chunk":
        if (typeof item.data === "string") {
          result.chunks.push(item.data);
          console.log("Added chunk", { length: item.data.length });
        }
        break;

      case "reference":
        if (this.isFileReference(item.data)) {
          result.references.push(item.data);
          console.log("Added reference", { filePath: item.data.file_path });
        }
        break;

      case "file_contents":
        if (Array.isArray(item.data) && item.data.length >= 3) {
          const fileContent: FileContent = {
            repo: item.data[0],
            file_path: item.data[1],
            content: item.data[2]
          };
          result.fileContents.push(fileContent);
          console.log("Added file content", {
            repo: fileContent.repo,
            filePath: fileContent.file_path,
            contentLength: fileContent.content.length
          });
        }
        break;

      default:
        console.log("Skipping unknown response type", { type: item.type });
    }
  }

  /**
   * 提取代码片段
   */
  extractCodeSnippets(result: ParsedResult): CodeSnippet[] {
    const snippets: CodeSnippet[] = [];

    for (const reference of result.references) {
      const fileContent = this.findFileContent(reference, result.fileContents);

      if (fileContent) {
        const snippet = this.extractSnippetFromFile(reference, fileContent);
        if (snippet) {
          snippets.push(snippet);
        }
      }
    }

    console.log(`Extracted ${snippets.length} code snippets`);
    return snippets;
  }

  /**
   * 查找文件内容
   */
  private findFileContent(reference: FileReference, fileContents: FileContent[]): FileContent | null {
    // 从 file_path 中提取仓库和文件路径
    const pathParts = reference.file_path.split(": ");
    if (pathParts.length < 2) {
      console.warn("Invalid file_path format", { filePath: reference.file_path });
      return null;
    }

    const repoPart = pathParts[0]?.replace("Repo ", "") || "";
    const filePart = pathParts[1]?.split(":")[0] || ""; // 移除行号部分

    return fileContents.find(fc => fc.repo === repoPart && fc.file_path === filePart) || null;
  }

  /**
   * 从文件中提取代码片段
   */
  private extractSnippetFromFile(reference: FileReference, fileContent: FileContent): CodeSnippet | null {
    try {
      const lines = fileContent.content.split("\n");
      const startLine = Math.max(0, reference.range_start - 1); // 转换为0基索引
      const endLine = Math.min(lines.length - 1, reference.range_end - 1);

      if (startLine > endLine || startLine >= lines.length) {
        console.warn("Invalid line range", {
          startLine: reference.range_start,
          endLine: reference.range_end,
          totalLines: lines.length
        });
        return null;
      }

      const snippetLines = lines.slice(startLine, endLine + 1);
      const content = snippetLines.join("\n");

      // 添加一些上下文行
      const contextStart = Math.max(0, startLine - 3);
      const contextEnd = Math.min(lines.length - 1, endLine + 3);
      const contextLines = lines.slice(contextStart, contextEnd + 1);
      const context = contextLines.join("\n");

      return {
        repo: fileContent.repo,
        filePath: fileContent.file_path,
        content,
        startLine: reference.range_start,
        endLine: reference.range_end,
        context
      };
    } catch (error) {
      console.error("Failed to extract snippet", { error, reference });
      return null;
    }
  }

  /**
   * 生成组合内容 - 将 chunks 拼接为总结内容，供大模型理解
   */
  generateCombinedContent(result: ParsedResult): string {
    const sections: string[] = [];

    // 添加文本块作为主要内容
    if (result.chunks.length > 0) {
      sections.push("## 总结内容\n");
      sections.push(result.chunks.join("\n\n"));
    }

    // 添加代码片段的简要说明
    if (result.codeSnippets.length > 0) {
      sections.push("\n## 相关代码引用\n");
      result.codeSnippets.forEach((snippet, index) => {
        sections.push(`${index + 1}. **${snippet.repo}/${snippet.filePath}** (行 ${snippet.startLine}-${snippet.endLine})`);
      });
    }

    return sections.join("\n");
  }

  /**
   * 生成摘要
   */
  generateSummary(result: ParsedResult): string {
    const parts: string[] = [];

    if (result.chunks.length > 0) {
      parts.push(`找到 ${result.chunks.length} 个相关代码块`);
    }

    if (result.references.length > 0) {
      parts.push(`${result.references.length} 个文件引用`);
    }

    if (result.fileContents.length > 0) {
      const repos = [...new Set(result.fileContents.map(fc => fc.repo))];
      parts.push(`来自 ${repos.length} 个仓库: ${repos.join(", ")}`);
    }

    return parts.join("，") || "未找到相关内容";
  }

  /**
   * 格式化结果为 Markdown
   */
  formatAsMarkdown(result: ParsedResult, snippets: CodeSnippet[]): string {
    const sections: string[] = [];

    // 摘要
    sections.push(`# DeepWiki 查询结果\n`);
    sections.push(`${this.generateSummary(result)}\n`);

    // 代码片段
    if (snippets.length > 0) {
      sections.push(`## 代码片段\n`);

      snippets.forEach((snippet, index) => {
        sections.push(`### ${index + 1}. ${snippet.repo}/${snippet.filePath}`);
        sections.push(`**行数**: ${snippet.startLine}-${snippet.endLine}\n`);
        sections.push("```" + this.getLanguageFromPath(snippet.filePath));
        sections.push(snippet.content);
        sections.push("```\n");
      });
    }

    // 文本块
    if (result.chunks.length > 0) {
      sections.push(`## 相关信息\n`);
      result.chunks.forEach((chunk, index) => {
        sections.push(`### 信息 ${index + 1}`);
        sections.push(chunk + "\n");
      });
    }

    return sections.join("\n");
  }

  /**
   * 根据文件路径推断编程语言
   */
  private getLanguageFromPath(filePath: string): string {
    const ext = filePath.split(".").pop()?.toLowerCase();
    const langMap: Record<string, string> = {
      js: "javascript",
      ts: "typescript",
      py: "python",
      java: "java",
      cpp: "cpp",
      c: "c",
      go: "go",
      rs: "rust",
      php: "php",
      rb: "ruby",
      swift: "swift",
      kt: "kotlin",
      scala: "scala",
      sh: "bash",
      yml: "yaml",
      yaml: "yaml",
      json: "json",
      xml: "xml",
      html: "html",
      css: "css",
      scss: "scss",
      md: "markdown"
    };

    return langMap[ext || ""] || "";
  }

  /**
   * 类型守卫：检查是否为文件引用
   */
  private isFileReference(data: any): data is FileReference {
    return (
      data && typeof data.file_path === "string" && typeof data.range_start === "number" && typeof data.range_end === "number"
    );
  }
}
