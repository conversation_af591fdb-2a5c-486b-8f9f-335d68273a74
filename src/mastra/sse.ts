#!/usr/bin/env node
import { MCPServer } from "@mastra/mcp";
import { blackhatWorldScrapeTool, blackhatWorldSearchTool, blackhatWorldTrendingTool } from "./tools/blacthatworld/index";
import { githubSearchTool, githubBatchSearchTool } from "./tools/github/index";
import { deepWikiSearchTool, deepWikiBatchSearchTool } from "./tools/deepwiki/index";
import { createServer, IncomingMessage, ServerResponse } from 'http';

const server = new MCPServer({
    name: "next-mcp-server",
    version: "1.0.0",
    tools: {
        blackhatWorldScrapeTool,
        blackhatWorldSearchTool,
        blackhatWorldTrendingTool,
        githubSearchTool,
        githubBatchSearchTool,
        deepWikiSearchTool,
        deepWikiBatchSearchTool
    }
});

// 创建 HTTP 服务器
const httpServer = createServer(async (req: IncomingMessage, res: ServerResponse) => {
    const url = new URL(req.url || '', `http://${req.headers.host}`);
    // const token = url.searchParams.get('token');
    // 鉴权
    // if (token !== "842797524") {
    //     res.writeHead(401, { 'Content-Type': 'text/plain' });
    //     res.end('Unauthorized');
    //     return;
    // }

    // 处理 SSE 连接和消息
    await server.startSSE({ url, ssePath: '/sse', messagePath: '/message', req, res });
});

const PORT = process.env.PORT || 3000;

httpServer.listen(+PORT, '0.0.0.0', () => {
    console.log(`MCP Server running on http://localhost:${PORT}/sse`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('Shutting down server...');
    httpServer.close(() => {
        process.exit(0);
    });
});