#!/usr/bin/env node
import 'dotenv/config';
import { MCPServer } from "@mastra/mcp";
import { blackhatWorldScrapeTool, blackhatWorldSearchTool, blackhatWorldTrendingTool } from "./tools/blacthatworld/index";
import { githubSearchTool, githubBatchSearchTool } from "./tools/github/index";
import { deepWikiSearchTool, deepWikiBatchSearchTool } from "./tools/deepwiki/index";
import { createServer, IncomingMessage, ServerResponse } from 'http';

const server = new MCPServer({
    name: "next-mcp-server",
    version: "1.0.0",
    tools: {
        blackhatWorldScrapeTool,
        blackhatWorldSearchTool,
        blackhatWorldTrendingTool,
        githubSearchTool,
        githubBatchSearchTool,
        deepWikiSearchTool,
        deepWikiBatchSearchTool
    }
});

// 创建 HTTP 服务器
const httpServer = createServer(async (req: IncomingMessage, res: ServerResponse) => {
    const url = new URL(req.url || '', `http://${req.headers.host}`);

    // 健康检查端点
    if (url.pathname === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ status: 'ok', timestamp: new Date().toISOString() }));
        return;
    }

    // 环境变量测试端点
    if (url.pathname === '/env-test') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            port: process.env.PORT,
            nodeEnv: process.env.NODE_ENV,
            hasFirecrawlKey: !!process.env.FIRECRAWL_API_KEY,
            hasGithubToken: !!process.env.GITHUB_TOKEN,
            timestamp: new Date().toISOString()
        }));
        return;
    }

    // const token = url.searchParams.get('token');
    // 鉴权
    // if (token !== "842797524") {
    //     res.writeHead(401, { 'Content-Type': 'text/plain' });
    //     res.end('Unauthorized');
    //     return;
    // }

    // 处理 SSE 连接和消息
    await server.startSSE({ url, ssePath: '/sse', messagePath: '/message', req, res });
});

const PORT = process.env.PORT || 3000;

httpServer.listen(+PORT, '0.0.0.0', () => {
    console.log(`MCP Server running on http://localhost:${PORT}/sse`);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('Shutting down server...');
    httpServer.close(() => {
        process.exit(0);
    });
});