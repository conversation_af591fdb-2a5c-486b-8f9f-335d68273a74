import { ProxyAgent } from "undici";
import { proxyManager } from "./manager";
import type { ProxyPoolConfig, ProxyStats } from "./types";
import pRetry, { AbortError, Options } from "p-retry";

/**
 * Get an available proxy from the pool.
 * Automatically initializes the pool on first use.
 *
 * @returns Promise<string | null> - Proxy URL or null if no healthy proxies available
 */
export const getAvailableProxy = async (): Promise<string | null> => {
  return proxyManager.getAvailableProxy();
};

/**
 * Record an error for a specific proxy.
 * This helps the pool track proxy health and remove unhealthy ones.
 *
 * @param proxyUrl - The proxy URL that encountered an error
 */
export const recordProxyError = (proxyUrl: string): void => {
  proxyManager.recordError(proxyUrl);
};

/**
 * Record a successful use of a proxy.
 * This helps improve the proxy's health score.
 *
 * @param proxyUrl - The proxy URL that was used successfully
 */
export const recordProxySuccess = (proxyUrl: string): void => {
  proxyManager.recordSuccess(proxyUrl);
};

// Advanced functions (optional, for monitoring and debugging)

/**
 * Get detailed statistics about the proxy pool.
 *
 * @returns Promise<ProxyStats | null> - Pool statistics or null if not initialized
 */
export const getProxyStats = async (): Promise<ProxyStats | null> => {
  return proxyManager.getStats();
};

/**
 * Manually trigger a health check for all proxies.
 * Note: This is a conceptual implementation.
 */
export const performHealthCheck = async (): Promise<void> => {
  return proxyManager.performHealthCheck();
};

/**
 * Initialize the proxy pool with custom configuration.
 *
 * @param config - Optional configuration for the proxy pool
 */
export const initializeProxyPool = async (config?: Partial<ProxyPoolConfig>): Promise<void> => {
  if (config) {
    await proxyManager.initializeWithConfig(config);
  } else {
    // Trigger initialization by getting a proxy
    await proxyManager.getAvailableProxy();
  }
};
/**
 * Retry fetch with a proxy.
 *
 * @param fetchFn - The fetch function to retry.
 * @param options - The options for the retry.
 * @param options.retries - The number of retries.
 * @param options.factor - The factor to use for the retry.
 * @param options.minTimeout - The minimum timeout for the retry.
 * @param options.maxTimeout - The maximum timeout for the retry.
 * @param options.randomize - Whether to randomize the timeout.
 * @param options.unref - Whether to unref the timeout.
 * @returns The response from the fetch function.
 */
export const retryFetch = async (
  fetchFn: (dispatcher: ProxyAgent, reportError: (err: Error) => void) => Promise<Response>,
  options?: Options
) => {
  const run = async () => {
    const proxy = await getAvailableProxy();
    if (!proxy) {
      throw new Error("No proxy available");
    }
    const dispatcher = new ProxyAgent(proxy);
    const reportError = (err: Error) => {
      recordProxyError(proxy);
      throw new Error(err.message);
    };
    return await fetchFn(dispatcher, reportError)
      .then(res => {
        recordProxySuccess(proxy);
        return res;
      })
      .catch(reportError);
  };
  return pRetry(run, { retries: 5, minTimeout: 0, ...options });
};

// Re-export types for advanced users
export type { ProxyPoolConfig, ProxyStats } from "./types";
